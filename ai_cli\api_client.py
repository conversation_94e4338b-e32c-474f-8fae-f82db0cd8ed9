"""
OpenRouter API client for AI CLI
"""

import json
import requests
from typing import Dict, List, Optional, Generator
from dataclasses import dataclass

from .config import Config


@dataclass
class Message:
    """Represents a chat message"""
    role: str  # 'user', 'assistant', or 'system'
    content: str


class APIError(Exception):
    """Custom exception for API errors"""
    pass


class OpenRouterClient:
    """Client for interacting with OpenRouter API"""

    def __init__(self, config: Config):
        self.config = config
        self.session = requests.Session()
        self.session.headers.update(config.headers)

    def chat_completion(self, messages: List[Message], stream: bool = False) -> Dict:
        """
        Send a chat completion request to OpenRouter API

        Args:
            messages: List of Message objects
            stream: Whether to stream the response

        Returns:
            API response as dictionary

        Raises:
            APIError: If the API request fails
        """
        url = f"{self.config.base_url}/chat/completions"

        payload = {
            "model": self.config.model,
            "messages": [{"role": msg.role, "content": msg.content} for msg in messages],
            "max_tokens": self.config.max_tokens,
            "temperature": self.config.temperature,
            "stream": stream
        }

        try:
            response = self.session.post(url, json=payload, timeout=30)
            response.raise_for_status()

            if stream:
                return self._handle_stream_response(response)
            else:
                return response.json()

        except requests.exceptions.RequestException as e:
            raise APIError(f"API request failed: {e}")
        except json.JSONDecodeError as e:
            raise APIError(f"Failed to parse API response: {e}")

    def _handle_stream_response(self, response) -> Generator[Dict, None, None]:
        """Handle streaming response from API"""
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data = line[6:]  # Remove 'data: ' prefix
                    if data.strip() == '[DONE]':
                        break
                    try:
                        yield json.loads(data)
                    except json.JSONDecodeError:
                        continue

    def get_available_models(self) -> List[Dict]:
        """
        Get list of available models from OpenRouter

        Returns:
            List of model information dictionaries
        """
        url = f"{self.config.base_url}/models"

        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            return response.json().get('data', [])
        except requests.exceptions.RequestException as e:
            raise APIError(f"Failed to fetch models: {e}")

    def test_connection(self) -> bool:
        """
        Test the API connection

        Returns:
            True if connection is successful, False otherwise
        """
        try:
            test_messages = [Message(role="user", content="Hello")]
            response = self.chat_completion(test_messages)
            return 'choices' in response and len(response['choices']) > 0
        except APIError:
            return False
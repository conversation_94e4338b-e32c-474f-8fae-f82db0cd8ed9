#!/usr/bin/env python3
"""
Test script for AI CLI application
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ai_cli.config import Config
from ai_cli.api_client import OpenRouterClient, Message
from ai_cli.utils import detect_code_language, format_code_block

def test_config():
    """Test configuration"""
    print("Testing configuration...")
    config = Config()
    print(f"✓ API Key: {'Set' if config.api_key else 'Not set'}")
    print(f"✓ Model: {config.model}")
    print(f"✓ Max Tokens: {config.max_tokens}")
    print(f"✓ Base URL: {config.base_url}")
    print()

def test_code_detection():
    """Test code language detection"""
    print("Testing code language detection...")

    test_cases = [
        ("def hello():\n    print('Hello, World!')", "python"),
        ("function hello() {\n    console.log('Hello, World!');\n}", "javascript"),
        ("SELECT * FROM users WHERE id = 1;", "sql"),
        ("#include <stdio.h>\nint main() {\n    printf('Hello');\n}", "c"),
    ]

    for code, expected in test_cases:
        detected = detect_code_language(code)
        status = "✓" if detected == expected else "✗"
        print(f"{status} Expected: {expected}, Detected: {detected}")
    print()

def test_api_client():
    """Test API client (without making actual requests)"""
    print("Testing API client initialization...")
    config = Config()
    client = OpenRouterClient(config)
    print("✓ API client initialized successfully")
    print(f"✓ Headers configured: {bool(client.config.headers)}")
    print()

def test_formatting():
    """Test code formatting"""
    print("Testing code formatting...")
    sample_code = """def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)"""

    try:
        formatted = format_code_block(sample_code, "python")
        print("✓ Code formatting works")
    except Exception as e:
        print(f"✗ Code formatting failed: {e}")
    print()

def main():
    """Run all tests"""
    print("🧪 AI CLI Test Suite")
    print("=" * 50)

    try:
        test_config()
        test_code_detection()
        test_api_client()
        test_formatting()

        print("🎉 All tests completed!")
        print("\nTo test the full application, run:")
        print("ai-cli chat")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
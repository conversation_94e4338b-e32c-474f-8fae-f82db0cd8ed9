"""
Interactive chat interface for AI CLI
"""

import sys
import re
from typing import List
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.markdown import Markdown
from rich.prompt import Prompt
from rich.live import Live
from rich.spinner import Spinner
from prompt_toolkit import prompt
from prompt_toolkit.history import InMemoryHistory
from prompt_toolkit.auto_suggest import AutoSuggestFromHistory
from prompt_toolkit.completion import WordCompleter

from .config import Config
from .api_client import OpenRouterClient, Message, APIError
from .utils import extract_code_blocks, format_code_block


class ChatInterface:
    """Interactive chat interface for the AI CLI"""

    def __init__(self, config: Config):
        self.config = config
        self.console = Console()
        self.client = OpenRouterClient(config)
        self.conversation_history: List[Message] = []
        self.history = InMemoryHistory()

        # Setup command completion
        self.completer = WordCompleter([
            '/help', '/clear', '/history', '/model', '/exit', '/quit'
        ])

    def start(self):
        """Start the interactive chat session"""
        self.console.print("[green]🚀 Starting AI Chat Session...[/green]")

        # Test API connection
        if not self._test_connection():
            return

        self.console.print(f"[cyan]Model:[/cyan] {self.config.model}")
        self.console.print(f"[cyan]Max Tokens:[/cyan] {self.config.max_tokens}")
        self.console.print()
        self.console.print("[yellow]Type '/help' for commands or start chatting![/yellow]")
        self.console.print("[dim]Press Ctrl+C to exit[/dim]")
        self.console.print()

        # Main chat loop
        while True:
            try:
                user_input = self._get_user_input()

                if not user_input.strip():
                    continue

                # Handle commands
                if user_input.startswith('/'):
                    if self._handle_command(user_input):
                        continue
                    else:
                        break

                # Process user message
                self._process_user_message(user_input)

            except KeyboardInterrupt:
                self.console.print("\n[yellow]Chat session ended. Goodbye![/yellow]")
                break
            except Exception as e:
                self.console.print(f"[red]Error: {e}[/red]")

    def _test_connection(self) -> bool:
        """Test the API connection"""
        with Live(Spinner("dots", text="Testing API connection..."), console=self.console):
            if self.client.test_connection():
                self.console.print("[green]✓ API connection successful![/green]")
                return True
            else:
                self.console.print("[red]✗ API connection failed. Please check your API key.[/red]")
                return False

    def _get_user_input(self) -> str:
        """Get user input with history and completion"""
        try:
            return prompt(
                "You: ",
                history=self.history,
                auto_suggest=AutoSuggestFromHistory(),
                completer=self.completer,
                complete_style="column"
            )
        except (EOFError, KeyboardInterrupt):
            raise KeyboardInterrupt

    def _handle_command(self, command: str) -> bool:
        """
        Handle chat commands

        Returns:
            True to continue chat, False to exit
        """
        command = command.lower().strip()

        if command in ['/exit', '/quit']:
            self.console.print("[yellow]Goodbye![/yellow]")
            return False

        elif command == '/help':
            self._show_help()

        elif command == '/clear':
            self.conversation_history.clear()
            self.console.clear()
            self.console.print("[green]Conversation history cleared![/green]")

        elif command == '/history':
            self._show_history()

        elif command == '/model':
            self.console.print(f"[cyan]Current model:[/cyan] {self.config.model}")

        else:
            self.console.print(f"[red]Unknown command: {command}[/red]")
            self.console.print("[yellow]Type '/help' for available commands[/yellow]")

        return True

    def _show_help(self):
        """Show help information"""
        help_text = """
Available Commands:
• /help     - Show this help message
• /clear    - Clear conversation history
• /history  - Show conversation history
• /model    - Show current AI model
• /exit     - Exit the chat session
• /quit     - Exit the chat session

Tips:
• Use Ctrl+C to exit at any time
• Multi-line input is supported
• Command history is available with arrow keys
        """
        self.console.print(Panel(help_text, title="Help", border_style="blue"))

    def _show_history(self):
        """Show conversation history"""
        if not self.conversation_history:
            self.console.print("[yellow]No conversation history yet.[/yellow]")
            return

        self.console.print(Panel("Conversation History", border_style="cyan"))
        for i, message in enumerate(self.conversation_history, 1):
            role_color = "green" if message.role == "user" else "blue"
            self.console.print(f"[{role_color}]{i}. {message.role.title()}:[/{role_color}]")
            self.console.print(f"   {message.content[:100]}{'...' if len(message.content) > 100 else ''}")
            self.console.print()

    def _process_user_message(self, user_input: str):
        """Process user message and get AI response"""
        # Add user message to history
        user_message = Message(role="user", content=user_input)
        self.conversation_history.append(user_message)

        # Prepare messages for API (include conversation context)
        messages = self.conversation_history.copy()

        try:
            # Show thinking indicator
            with Live(Spinner("dots", text="AI is thinking..."), console=self.console):
                response = self.client.chat_completion(messages)

            # Extract AI response
            if 'choices' in response and len(response['choices']) > 0:
                ai_content = response['choices'][0]['message']['content']
                ai_message = Message(role="assistant", content=ai_content)
                self.conversation_history.append(ai_message)

                # Display AI response with formatting
                self._display_ai_response(ai_content)
            else:
                self.console.print("[red]Error: No response from AI[/red]")

        except APIError as e:
            self.console.print(f"[red]API Error: {e}[/red]")
        except Exception as e:
            self.console.print(f"[red]Unexpected error: {e}[/red]")

    def _display_ai_response(self, content: str):
        """Display AI response with proper formatting"""
        self.console.print()
        self.console.print("[blue]AI:[/blue]")

        # Extract and format code blocks separately
        code_blocks = extract_code_blocks(content)

        if code_blocks:
            # If there are code blocks, process them specially
            remaining_content = content

            for code, language in code_blocks:
                # Remove the code block from content for markdown rendering
                code_pattern = f"```{language or ''}\n{re.escape(code)}\n```"
                remaining_content = re.sub(code_pattern, "[CODE_BLOCK_PLACEHOLDER]", remaining_content, count=1)

            # Render remaining content as markdown
            try:
                if remaining_content.strip() and remaining_content.strip() != "[CODE_BLOCK_PLACEHOLDER]":
                    # Replace placeholders and render
                    parts = remaining_content.split("[CODE_BLOCK_PLACEHOLDER]")

                    for i, part in enumerate(parts):
                        if part.strip():
                            markdown = Markdown(part)
                            self.console.print(markdown)

                        # Display code block if there's one after this part
                        if i < len(code_blocks):
                            code, language = code_blocks[i]
                            formatted_code = format_code_block(code, language)
                            self.console.print(formatted_code)
                else:
                    # Only code blocks, display them
                    for code, language in code_blocks:
                        formatted_code = format_code_block(code, language)
                        self.console.print(formatted_code)

            except Exception:
                # Fallback to plain text if markdown fails
                self.console.print(content)
        else:
            # No code blocks, render as markdown
            try:
                markdown = Markdown(content)
                self.console.print(markdown)
            except Exception:
                # Fallback to plain text if markdown fails
                self.console.print(content)

        self.console.print()
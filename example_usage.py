#!/usr/bin/env python3
"""
Example usage of AI CLI programmatically
"""

from ai_cli.config import Config
from ai_cli.api_client import OpenRouterClient, Message, APIError

def example_api_usage():
    """Example of using the API client directly"""
    print("🤖 AI CLI API Example")
    print("=" * 40)

    # Initialize configuration
    config = Config()
    client = OpenRouterClient(config)

    # Test connection
    print("Testing API connection...")
    if client.test_connection():
        print("✓ API connection successful!")
    else:
        print("✗ API connection failed. This might be due to:")
        print("  - Network connectivity issues")
        print("  - API key limitations")
        print("  - OpenRouter service availability")
        print("\nThe CLI application is still functional for offline testing.")
        return

    # Example conversation
    messages = [
        Message(role="user", content="Hello! Can you write a simple Python function to add two numbers?")
    ]

    try:
        print("\nSending request to AI...")
        response = client.chat_completion(messages)

        if 'choices' in response and len(response['choices']) > 0:
            ai_response = response['choices'][0]['message']['content']
            print("\n🤖 AI Response:")
            print("-" * 40)
            print(ai_response)
        else:
            print("No response received from AI")

    except APIError as e:
        print(f"API Error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")

def show_cli_commands():
    """Show available CLI commands"""
    print("\n📋 Available CLI Commands:")
    print("=" * 40)
    print("ai-cli --help              # Show help")
    print("ai-cli info                # Show application info")
    print("ai-cli chat                # Start interactive chat")
    print("ai-cli chat --model MODEL  # Use specific model")
    print("ai-cli chat --api-key KEY  # Use custom API key")

    print("\n💡 Interactive Commands (within chat):")
    print("/help     # Show help")
    print("/clear    # Clear history")
    print("/history  # Show conversation history")
    print("/model    # Show current model")
    print("/exit     # Exit chat")

if __name__ == "__main__":
    example_api_usage()
    show_cli_commands()

    print("\n🚀 To start the interactive chat, run:")
    print("ai-cli chat")